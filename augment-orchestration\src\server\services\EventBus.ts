import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { EventBusMessage } from '@/shared/types';
import { logger } from '../utils/logger';

export class EventBus extends EventEmitter {
  private messageHistory: EventBusMessage[] = [];
  private maxHistorySize: number = 1000;

  constructor() {
    super();
    this.setMaxListeners(100); // Increase max listeners for high-throughput scenarios
  }

  public async initialize(): Promise<void> {
    logger.info('EventBus initialized');
  }

  /**
   * Publish a message to the event bus
   */
  public publish(type: string, payload: any, source: string, target?: string, metadata?: Record<string, any>): void {
    const message: EventBusMessage = {
      id: uuidv4(),
      type,
      source,
      target,
      payload,
      timestamp: new Date(),
      metadata,
    };

    // Add to history
    this.addToHistory(message);

    // Emit the event
    this.emit(type, message);
    this.emit('*', message); // Wildcard listener

    logger.debug(`EventBus: Published message`, {
      type,
      source,
      target,
      messageId: message.id,
    });
  }

  /**
   * Subscribe to specific event types
   */
  public subscribe(type: string, handler: (message: EventBusMessage) => void): () => void {
    this.on(type, handler);
    
    logger.debug(`EventBus: Subscribed to event type: ${type}`);
    
    // Return unsubscribe function
    return () => {
      this.off(type, handler);
      logger.debug(`EventBus: Unsubscribed from event type: ${type}`);
    };
  }

  /**
   * Subscribe to all events (wildcard)
   */
  public subscribeAll(handler: (message: EventBusMessage) => void): () => void {
    this.on('*', handler);
    
    logger.debug('EventBus: Subscribed to all events');
    
    return () => {
      this.off('*', handler);
      logger.debug('EventBus: Unsubscribed from all events');
    };
  }

  /**
   * Get message history
   */
  public getHistory(type?: string, limit?: number): EventBusMessage[] {
    let history = this.messageHistory;
    
    if (type) {
      history = history.filter(msg => msg.type === type);
    }
    
    if (limit) {
      history = history.slice(-limit);
    }
    
    return history;
  }

  /**
   * Clear message history
   */
  public clearHistory(): void {
    this.messageHistory = [];
    logger.info('EventBus: Message history cleared');
  }

  /**
   * Get event bus statistics
   */
  public getStats(): {
    totalMessages: number;
    eventTypes: Record<string, number>;
    listeners: Record<string, number>;
  } {
    const eventTypes: Record<string, number> = {};
    
    this.messageHistory.forEach(msg => {
      eventTypes[msg.type] = (eventTypes[msg.type] || 0) + 1;
    });

    const listeners: Record<string, number> = {};
    this.eventNames().forEach(eventName => {
      listeners[eventName.toString()] = this.listenerCount(eventName);
    });

    return {
      totalMessages: this.messageHistory.length,
      eventTypes,
      listeners,
    };
  }

  private addToHistory(message: EventBusMessage): void {
    this.messageHistory.push(message);
    
    // Trim history if it exceeds max size
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory = this.messageHistory.slice(-this.maxHistorySize);
    }
  }
}

// Event type constants for type safety
export const EVENT_TYPES = {
  // Orchestrator Events
  ORCHESTRATOR_CREATED: 'orchestrator:created',
  ORCHESTRATOR_UPDATED: 'orchestrator:updated',
  ORCHESTRATOR_DELETED: 'orchestrator:deleted',
  
  // Agent Events
  AGENT_ASSIGNED: 'agent:assigned',
  AGENT_STATUS_CHANGED: 'agent:status_changed',
  AGENT_EVOLVED: 'agent:evolved',
  
  // Tunnel Events
  TUNNEL_CREATED: 'tunnel:created',
  TUNNEL_ACTIVATED: 'tunnel:activated',
  TUNNEL_DATA_FLOW: 'tunnel:data_flow',
  
  // Workflow Events
  WORKFLOW_STARTED: 'workflow:started',
  WORKFLOW_PROGRESS: 'workflow:progress',
  WORKFLOW_COMPLETED: 'workflow:completed',
  WORKFLOW_FAILED: 'workflow:failed',
  
  // System Events
  SYSTEM_STATUS: 'system:status',
  USER_PRESENCE: 'user:presence',
  
  // Evolution Events
  EVOLUTION_MUTATION: 'evolution:mutation',
  EVOLUTION_SELECTION: 'evolution:selection',
  EVOLUTION_PROMOTION: 'evolution:promotion',
  
  // Context Events
  CONTEXT_UPDATED: 'context:updated',
  CONTEXT_SHARED: 'context:shared',
} as const;
