// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdOrchestrators MetaOrchestrator[]
  auditLogs           AuditLog[]
  workflowExecutions  WorkflowExecution[]

  @@map("users")
}

enum UserRole {
  ADMIN
  USER
  VIEWER
}

model MetaOrchestrator {
  id          String   @id @default(cuid())
  name        String
  description String?
  version     String   @default("1.0.0")
  isActive    Boolean  @default(true)
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  creator           User                @relation(fields: [creatorId], references: [id])
  creatorId         String
  subOrchestrators  SubOrchestrator[]
  workflowTemplates WorkflowTemplate[]
  tunnels           Tunnel[]

  @@map("meta_orchestrators")
}

model SubOrchestrator {
  id          String   @id @default(cuid())
  name        String
  description String?
  domain      String
  isActive    Boolean  @default(true)
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  metaOrchestrator   MetaOrchestrator @relation(fields: [metaOrchestratorId], references: [id], onDelete: Cascade)
  metaOrchestratorId String
  agents             Agent[]
  tunnelsFrom        Tunnel[]         @relation("TunnelFrom")
  tunnelsTo          Tunnel[]         @relation("TunnelTo")

  @@map("sub_orchestrators")
}

model Agent {
  id           String    @id @default(cuid())
  agentId      String    @unique // e.g., "chatgpt-4o", "claude-opus"
  name         String
  description  String?
  vendor       String    // e.g., "OpenAI", "Anthropic", "Google"
  capabilities String[]  // e.g., ["code-generation", "validation"]
  roles        String[]  // e.g., ["generator", "validator"]
  isActive     Boolean   @default(true)
  metadata     Json?
  fitnessScore Float     @default(0.0)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  subOrchestrator     SubOrchestrator?   @relation(fields: [subOrchestratorId], references: [id])
  subOrchestratorId   String?
  workflowExecutions  WorkflowExecution[]
  evolutionVariants   EvolutionVariant[]
  tunnelsFrom         Tunnel[]           @relation("AgentTunnelFrom")
  tunnelsTo           Tunnel[]           @relation("AgentTunnelTo")

  @@map("agents")
}

model Tunnel {
  id          String     @id @default(cuid())
  name        String
  description String?
  tags        String[]   // e.g., ["mining↔medicine", "cross-domain"]
  isActive    Boolean    @default(true)
  metadata    Json?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations - Flexible tunnel connections
  metaOrchestrator   MetaOrchestrator? @relation(fields: [metaOrchestratorId], references: [id])
  metaOrchestratorId String?

  fromSubOrchestrator   SubOrchestrator? @relation("TunnelFrom", fields: [fromSubOrchestratorId], references: [id])
  fromSubOrchestratorId String?
  toSubOrchestrator     SubOrchestrator? @relation("TunnelTo", fields: [toSubOrchestratorId], references: [id])
  toSubOrchestratorId   String?

  fromAgent   Agent?  @relation("AgentTunnelFrom", fields: [fromAgentId], references: [id])
  fromAgentId String?
  toAgent     Agent?  @relation("AgentTunnelTo", fields: [toAgentId], references: [id])
  toAgentId   String?

  @@map("tunnels")
}

model WorkflowTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  stages      Json     // Array of workflow stages
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  metaOrchestrator   MetaOrchestrator    @relation(fields: [metaOrchestratorId], references: [id], onDelete: Cascade)
  metaOrchestratorId String
  executions         WorkflowExecution[]

  @@map("workflow_templates")
}

model WorkflowExecution {
  id        String            @id @default(cuid())
  status    WorkflowStatus    @default(PENDING)
  startedAt DateTime          @default(now())
  endedAt   DateTime?
  result    Json?
  metadata  Json?

  // Relations
  template   WorkflowTemplate @relation(fields: [templateId], references: [id])
  templateId String
  executor   User             @relation(fields: [executorId], references: [id])
  executorId String
  agents     Agent[]

  @@map("workflow_executions")
}

enum WorkflowStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

model EvolutionVariant {
  id           String   @id @default(cuid())
  generation   Int
  mutations    Json     // Array of mutations applied
  fitnessScore Float
  isPromoted   Boolean  @default(false)
  createdAt    DateTime @default(now())

  // Relations
  parentAgent Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId     String

  @@map("evolution_variants")
}

model AuditLog {
  id        String   @id @default(cuid())
  action    String
  entityId  String
  entityType String
  oldValue  Json?
  newValue  Json?
  metadata  Json?
  timestamp DateTime @default(now())

  // Relations
  user   User   @relation(fields: [userId], references: [id])
  userId String

  @@map("audit_logs")
}

model SharedContext {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  tags      String[]
  expiresAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("shared_contexts")
}
