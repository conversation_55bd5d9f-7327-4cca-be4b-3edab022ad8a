import React, { useEffect, useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchAgents } from '../store/slices/agentSlice';
import TunnelVisualization from '../components/TunnelVisualization';
import { tunnelApi } from '../services/api';

const Tunnels: React.FC = () => {
  const dispatch = useAppDispatch();
  const { agents, isLoading: agentLoading } = useAppSelector(state => state.agent);

  const [tunnels, setTunnels] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    dispatch(fetchAgents());
    loadTunnels();
  }, [dispatch]);

  const loadTunnels = async () => {
    try {
      setIsLoading(true);
      const response = await tunnelApi.getAll();
      setTunnels(response.data.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load tunnels');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTunnel = async (tunnelData: any) => {
    try {
      await tunnelApi.create(tunnelData);
      loadTunnels(); // Refresh the list
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create tunnel');
    }
  };

  const handleUpdateTunnel = async (id: string, tunnelData: any) => {
    try {
      await tunnelApi.update(id, tunnelData);
      loadTunnels(); // Refresh the list
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update tunnel');
    }
  };

  const handleDeleteTunnel = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this tunnel?')) {
      try {
        await tunnelApi.delete(id);
        loadTunnels(); // Refresh the list
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to delete tunnel');
      }
    }
  };

  const handleSendData = async (tunnelId: string, data: any) => {
    try {
      await tunnelApi.sendData(tunnelId, data);
      // Show success message or update UI as needed
      alert('Data sent successfully through tunnel!');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to send data');
    }
  };

  if (isLoading || agentLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        🚇 Cross-Domain Tunnels
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        Persistent bidirectional workflow links between agents and orchestrators
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <TunnelVisualization
        tunnels={tunnels}
        agents={agents}
        onCreateTunnel={handleCreateTunnel}
        onUpdateTunnel={handleUpdateTunnel}
        onDeleteTunnel={handleDeleteTunnel}
        onSendData={handleSendData}
      />
    </Container>
  );
};

export default Tunnels;
