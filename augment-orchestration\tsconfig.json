{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "jsx": "react-jsx", "declaration": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "moduleDetection": "force", "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/shared/*": ["src/shared/*"], "@/server/*": ["src/server/*"], "@/client/*": ["client/src/*"]}}, "include": ["src/**/*", "client/src/**/*"], "exclude": ["node_modules", "dist", "client/dist", "**/*.test.ts", "**/*.test.tsx"]}