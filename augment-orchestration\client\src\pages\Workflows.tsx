import React, { useEffect, useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchWorkflowTemplates } from '../store/slices/workflowSlice';
import WorkflowExecutionDashboard from '../components/WorkflowExecutionDashboard';
import { workflowApi } from '../services/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`workflow-tabpanel-${index}`}
      aria-labelledby={`workflow-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Workflows: React.FC = () => {
  const dispatch = useAppDispatch();
  const { templates, isLoading: templatesLoading } = useAppSelector(state => state.workflow);

  const [executions, setExecutions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    dispatch(fetchWorkflowTemplates());
    loadExecutions();
  }, [dispatch]);

  const loadExecutions = async () => {
    try {
      setIsLoading(true);
      const response = await workflowApi.getExecutions();
      setExecutions(response.data.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load executions');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartExecution = async (templateId: string, context: any) => {
    try {
      await workflowApi.startExecution(templateId, context);
      loadExecutions(); // Refresh the list
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to start execution');
    }
  };

  const handleCancelExecution = async (executionId: string) => {
    if (window.confirm('Are you sure you want to cancel this execution?')) {
      try {
        await workflowApi.cancelExecution(executionId);
        loadExecutions(); // Refresh the list
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to cancel execution');
      }
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (isLoading || templatesLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        🔄 Workflow Management
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        Multi-agent workflow orchestration and real-time execution monitoring
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="workflow tabs">
          <Tab label="Executions" />
          <Tab label="Templates" />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <WorkflowExecutionDashboard
          executions={executions}
          templates={templates}
          onStartExecution={handleStartExecution}
          onCancelExecution={handleCancelExecution}
          onRefresh={loadExecutions}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Typography variant="h6" gutterBottom>
          Workflow Templates
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Template management will be implemented here, including:
        </Typography>
        <Box component="ul" sx={{ mt: 2 }}>
          <li>Create and edit workflow templates</li>
          <li>Define stages and dependencies</li>
          <li>Configure agent role requirements</li>
          <li>Set execution parameters and timeouts</li>
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" gutterBottom>
          Workflow Analytics
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Analytics dashboard will be implemented here, including:
        </Typography>
        <Box component="ul" sx={{ mt: 2 }}>
          <li>Execution success rates and performance metrics</li>
          <li>Agent utilization and workload distribution</li>
          <li>Stage completion times and bottleneck analysis</li>
          <li>Resource usage and optimization recommendations</li>
        </Box>
      </TabPanel>
    </Container>
  );
};

export default Workflows;
