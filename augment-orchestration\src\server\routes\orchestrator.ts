import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createOrchestratorSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),
  metadata: z.record(z.any()).optional(),
});

const updateOrchestratorSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  description: z.string().optional(),
  version: z.string().optional(),
  isActive: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

// Get all meta-orchestrators
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;

  const skip = (page - 1) * limit;

  const where: any = {};
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }
  
  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  const [orchestrators, total] = await Promise.all([
    prisma.metaOrchestrator.findMany({
      where,
      skip,
      take: limit,
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
        subOrchestrators: {
          select: {
            id: true,
            name: true,
            domain: true,
            isActive: true,
          },
        },
        _count: {
          select: {
            subOrchestrators: true,
            workflowTemplates: true,
            tunnels: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.metaOrchestrator.count({ where }),
  ]);

  res.json({
    success: true,
    data: orchestrators,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get single meta-orchestrator
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const orchestrator = await prisma.metaOrchestrator.findUnique({
    where: { id },
    include: {
      creator: {
        select: {
          id: true,
          username: true,
          email: true,
        },
      },
      subOrchestrators: {
        include: {
          agents: {
            select: {
              id: true,
              agentId: true,
              name: true,
              vendor: true,
              capabilities: true,
              roles: true,
              isActive: true,
              fitnessScore: true,
            },
          },
          _count: {
            select: {
              agents: true,
              tunnelsFrom: true,
              tunnelsTo: true,
            },
          },
        },
      },
      workflowTemplates: {
        select: {
          id: true,
          name: true,
          description: true,
          stages: true,
          createdAt: true,
        },
      },
      tunnels: {
        select: {
          id: true,
          name: true,
          description: true,
          tags: true,
          isActive: true,
          fromSubOrchestratorId: true,
          toSubOrchestratorId: true,
          fromAgentId: true,
          toAgentId: true,
        },
      },
    },
  });

  if (!orchestrator) {
    throw new AppError('Meta-orchestrator not found', 404);
  }

  res.json({
    success: true,
    data: orchestrator,
  });
}));

// Create new meta-orchestrator
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const data = createOrchestratorSchema.parse(req.body);

  const orchestrator = await prisma.metaOrchestrator.create({
    data: {
      ...data,
      creatorId: req.user!.id,
    },
    include: {
      creator: {
        select: {
          id: true,
          username: true,
          email: true,
        },
      },
    },
  });

  logger.info(`Meta-orchestrator created: ${orchestrator.name} by ${req.user!.username}`);

  res.status(201).json({
    success: true,
    data: orchestrator,
    message: 'Meta-orchestrator created successfully',
  });
}));

// Update meta-orchestrator
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const data = updateOrchestratorSchema.parse(req.body);

  // Check if orchestrator exists and user has permission
  const existingOrchestrator = await prisma.metaOrchestrator.findUnique({
    where: { id },
    select: { creatorId: true, name: true },
  });

  if (!existingOrchestrator) {
    throw new AppError('Meta-orchestrator not found', 404);
  }

  if (existingOrchestrator.creatorId !== req.user!.id && req.user!.role !== 'ADMIN') {
    throw new AppError('Permission denied', 403);
  }

  const orchestrator = await prisma.metaOrchestrator.update({
    where: { id },
    data,
    include: {
      creator: {
        select: {
          id: true,
          username: true,
          email: true,
        },
      },
    },
  });

  logger.info(`Meta-orchestrator updated: ${orchestrator.name} by ${req.user!.username}`);

  res.json({
    success: true,
    data: orchestrator,
    message: 'Meta-orchestrator updated successfully',
  });
}));

// Delete meta-orchestrator
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if orchestrator exists and user has permission
  const existingOrchestrator = await prisma.metaOrchestrator.findUnique({
    where: { id },
    select: { creatorId: true, name: true },
  });

  if (!existingOrchestrator) {
    throw new AppError('Meta-orchestrator not found', 404);
  }

  if (existingOrchestrator.creatorId !== req.user!.id && req.user!.role !== 'ADMIN') {
    throw new AppError('Permission denied', 403);
  }

  await prisma.metaOrchestrator.delete({
    where: { id },
  });

  logger.info(`Meta-orchestrator deleted: ${existingOrchestrator.name} by ${req.user!.username}`);

  res.json({
    success: true,
    message: 'Meta-orchestrator deleted successfully',
  });
}));

export { router as orchestratorRoutes };
