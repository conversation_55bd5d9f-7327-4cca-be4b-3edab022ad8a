import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { ApiResponse, PaginatedResponse } from '@/shared/types'

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: (email: string, password: string): Promise<AxiosResponse<ApiResponse<{ user: any; token: string }>>> =>
    api.post('/auth/login', { email, password }),
  
  register: (email: string, username: string, password: string): Promise<AxiosResponse<ApiResponse<{ user: any; token: string }>>> =>
    api.post('/auth/register', { email, username, password }),
  
  getCurrentUser: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/auth/me'),
  
  refreshToken: (): Promise<AxiosResponse<ApiResponse<{ token: string }>>> =>
    api.post('/auth/refresh'),
}

// Orchestrator API
export const orchestratorApi = {
  getAll: (params?: any): Promise<AxiosResponse<PaginatedResponse<any>>> =>
    api.get('/orchestrators', { params }),
  
  getById: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/orchestrators/${id}`),
  
  create: (data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/orchestrators', data),
  
  update: (id: string, data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.put(`/orchestrators/${id}`, data),
  
  delete: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.delete(`/orchestrators/${id}`),
}

// Agent API
export const agentApi = {
  getAll: (params?: any): Promise<AxiosResponse<PaginatedResponse<any>>> =>
    api.get('/agents', { params }),

  getById: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/agents/${id}`),

  getRoles: (): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.get('/agents/roles'),

  getRegistry: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/agents/registry'),

  create: (data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/agents', data),

  update: (id: string, data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.put(`/agents/${id}`, data),

  delete: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.delete(`/agents/${id}`),

  assign: (criteria: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/agents/assign', criteria),

  assignRoles: (data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/agents/assign-roles', data),

  getRoleRecommendations: (roleId: string): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.get(`/agents/recommendations/${roleId}`),

  getCapabilitiesAnalysis: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/agents/capabilities-analysis'),
}

// Workflow API
export const workflowApi = {
  getTemplates: (params?: any): Promise<AxiosResponse<PaginatedResponse<any>>> =>
    api.get('/workflows/templates', { params }),

  getExecutions: (params?: any): Promise<AxiosResponse<PaginatedResponse<any>>> =>
    api.get('/workflows/executions', { params }),

  getExecutionStatus: (executionId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/workflows/executions/${executionId}`),

  startExecution: (templateId: string, context: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post(`/workflows/${templateId}/execute`, { context }),

  cancelExecution: (executionId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post(`/workflows/executions/${executionId}/cancel`),

  createTemplate: (data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/workflows/templates', data),
}

// Tunnel API
export const tunnelApi = {
  getAll: (params?: any): Promise<AxiosResponse<PaginatedResponse<any>>> =>
    api.get('/tunnels', { params }),

  getById: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/tunnels/${id}`),

  create: (data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/tunnels', data),

  update: (id: string, data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.put(`/tunnels/${id}`, data),

  delete: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.delete(`/tunnels/${id}`),

  sendData: (id: string, data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post(`/tunnels/${id}/send`, data),
}

// Evolution API
export const evolutionApi = {
  getVariants: (): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.get('/evolution/variants'),

  getStatus: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/evolution/status'),

  getHistory: (limit?: number): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.get('/evolution/history', { params: { limit } }),

  startEvolution: (parameters: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/evolution/start', { parameters }),

  stopEvolution: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/evolution/stop'),
}

// Audit API
export const auditApi = {
  getLogs: (params?: any): Promise<AxiosResponse<PaginatedResponse<any>>> =>
    api.get('/audit', { params }),
}

// Context API
export const contextApi = {
  getAll: (): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.get('/context'),
  
  create: (data: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/context', data),
}

export default api
