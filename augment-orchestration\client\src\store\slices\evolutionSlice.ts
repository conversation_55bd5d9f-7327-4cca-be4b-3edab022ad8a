import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { EvolutionVariant } from '@/shared/types'
import { evolutionApi } from '../../services/api'

interface EvolutionState {
  variants: EvolutionVariant[]
  isLoading: boolean
  error: string | null
}

const initialState: EvolutionState = {
  variants: [],
  isLoading: false,
  error: null,
}

export const fetchEvolutionVariants = createAsyncThunk(
  'evolution/fetchVariants',
  async () => {
    const response = await evolutionApi.getVariants()
    return response.data
  }
)

const evolutionSlice = createSlice({
  name: 'evolution',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchEvolutionVariants.fulfilled, (state, action) => {
        state.variants = action.payload
      })
  },
})

export const { clearError } = evolutionSlice.actions
export default evolutionSlice.reducer
