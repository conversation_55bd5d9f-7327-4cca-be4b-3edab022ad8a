import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { PrismaClient } from '@prisma/client';
import { config } from './config';
import { logger } from './utils/logger';
import { EventBus } from './services/EventBus';
import { SocketService } from './services/SocketService';
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';

// Import route handlers
import { orchestratorRoutes } from './routes/orchestrator';
import { agentRoutes } from './routes/agent';
import { tunnelRoutes } from './routes/tunnel';
import { workflowRoutes } from './routes/workflow';
import { evolutionRoutes } from './routes/evolution';
import { auditRoutes } from './routes/audit';
import { contextRoutes } from './routes/context';
import { authRoutes } from './routes/auth';

class AugmentOrchestrationServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private prisma: PrismaClient;
  private eventBus: EventBus;
  private socketService: SocketService;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.prisma = new PrismaClient();
    this.eventBus = new EventBus();
    
    this.setupSocketIO();
    this.socketService = new SocketService(this.io, this.eventBus);
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupSocketIO(): void {
    this.io = new SocketIOServer(this.server, {
      cors: config.socket.cors,
      transports: config.socket.transports as any[]
    });

    logger.info('Socket.IO server initialized');
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"],
        },
      },
    }));

    // CORS
    this.app.use(cors(config.server.cors));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.server.rateLimit.windowMs,
      max: config.server.rateLimit.max,
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      next();
    });

    logger.info('Middleware setup completed');
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // Authentication routes (no auth required)
    this.app.use('/api/auth', authRoutes);

    // Protected API routes
    this.app.use('/api/orchestrators', authMiddleware, orchestratorRoutes);
    this.app.use('/api/agents', authMiddleware, agentRoutes);
    this.app.use('/api/tunnels', authMiddleware, tunnelRoutes);
    this.app.use('/api/workflows', authMiddleware, workflowRoutes);
    this.app.use('/api/evolution', authMiddleware, evolutionRoutes);
    this.app.use('/api/audit', authMiddleware, auditRoutes);
    this.app.use('/api/context', authMiddleware, contextRoutes);

    // Serve static files in production
    if (process.env.NODE_ENV === 'production') {
      this.app.use(express.static('client/dist'));
      this.app.get('*', (req, res) => {
        res.sendFile('index.html', { root: 'client/dist' });
      });
    }

    logger.info('Routes setup completed');
  }

  private setupErrorHandling(): void {
    this.app.use(errorHandler);
    
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    logger.info('Error handling setup completed');
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await this.prisma.$connect();
      logger.info('Database connected successfully');

      // Start server
      this.server.listen(config.server.port, config.server.host, () => {
        logger.info(`🚀 Augment Orchestration Server running at http://${config.server.host}:${config.server.port}`);
        logger.info(`📊 Dashboard: http://${config.server.host}:${config.server.port}`);
        logger.info(`🔌 WebSocket connections: 0`);
      });

      // Initialize services
      await this.eventBus.initialize();
      await this.socketService.initialize();

      logger.info('Server started successfully');
    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      this.server.close();
      logger.info('Server stopped successfully');
    } catch (error) {
      logger.error('Error stopping server:', error);
    }
  }
}

// Start server
const server = new AugmentOrchestrationServer();

if (require.main === module) {
  server.start().catch((error) => {
    logger.error('Failed to start server:', error);
    process.exit(1);
  });
}

export default server;
