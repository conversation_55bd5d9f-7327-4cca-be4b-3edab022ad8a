{"name": "augment-orchestration-client", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.19", "@reduxjs/toolkit": "^1.9.7", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "react": "^18.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-redux": "^8.1.3", "react-router-dom": "^6.18.0", "socket.io-client": "^4.7.4", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^4.5.0"}}