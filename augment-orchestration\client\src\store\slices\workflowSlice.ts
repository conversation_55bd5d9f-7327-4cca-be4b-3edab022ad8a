import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { WorkflowTemplate, WorkflowExecution } from '@/shared/types'
import { workflowApi } from '../../services/api'

interface WorkflowState {
  templates: WorkflowTemplate[]
  executions: WorkflowExecution[]
  isLoading: boolean
  error: string | null
}

const initialState: WorkflowState = {
  templates: [],
  executions: [],
  isLoading: false,
  error: null,
}

export const fetchWorkflowTemplates = createAsyncThunk(
  'workflow/fetchTemplates',
  async () => {
    const response = await workflowApi.getTemplates()
    return response.data
  }
)

export const fetchWorkflowExecutions = createAsyncThunk(
  'workflow/fetchExecutions',
  async () => {
    const response = await workflowApi.getExecutions()
    return response.data
  }
)

const workflowSlice = createSlice({
  name: 'workflow',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWorkflowTemplates.fulfilled, (state, action) => {
        state.templates = action.payload
      })
      .addCase(fetchWorkflowExecutions.fulfilled, (state, action) => {
        state.executions = action.payload
      })
  },
})

export const { clearError } = workflowSlice.actions
export default workflowSlice.reducer
