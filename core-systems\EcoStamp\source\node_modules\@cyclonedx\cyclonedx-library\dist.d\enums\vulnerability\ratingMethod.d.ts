/*!
This file is part of CycloneDX JavaScript Library.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

SPDX-License-Identifier: Apache-2.0
Copyright (c) OWASP Foundation. All Rights Reserved.
*/
/**
 * Specifies the severity or risk scoring methodology or standard used.
 */
export declare enum RatingMethod {
    /** CVSSv2 - [Common Vulnerability Scoring System v2](https://www.first.org/cvss/v2/) */
    CVSSv2 = "CVSSv2",
    /** CVSSv3 - [Common Vulnerability Scoring System v3](https://www.first.org/cvss/v3-0/) */
    CVSSv3 = "CVSSv3",
    /** CVSSv31 - [Common Vulnerability Scoring System v3.1](https://www.first.org/cvss/v3-1/) */
    CVSSv31 = "CVSSv31",
    /** CVSSv4 - [Common Vulnerability Scoring System v4](https://www.first.org/cvss/v4-0/) */
    CVSSv4 = "CVSSv4",
    /** OWASP - [OWASP Risk Rating Methodology](https://owasp.org/www-community/OWASP_Risk_Rating_Methodology) */
    OWASP = "OWASP",
    /** SSVC - [Stakeholder Specific Vulnerability Categorization](https://github.com/CERTCC/SSVC) (all versions) */
    SSVC = "SSVC",
    /** any other */
    Other = "other"
}
//# sourceMappingURL=ratingMethod.d.ts.map